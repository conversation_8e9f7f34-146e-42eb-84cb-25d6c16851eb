# Remote SSH Gateway

A remote device SSH gateway that bridges SSH connections with WebSocket connections to remote devices.

## Architecture

This system acts as a bridge between SSH clients (like JumpServer) and remote devices connected via WebSocket. The architecture includes:

- **SSH Server**: Accepts SSH connections and handles authentication
- **WebSocket Server**: Manages connections from remote devices
- **Connection Manager**: Coordinates data flow between SSH and WebSocket connections

## Project Structure

```
remote-ssh-gateway/
├── keys/
│   ├── ssh_host_key      # SSH host private key
│   └── ssh_host_key.pub  # SSH host public key
├── ssh_gateway/
│   ├── __init__.py       # Package initialization
│   ├── config.py         # Configuration settings
│   ├── ws_manager.py     # WebSocket connection manager
│   └── ssh_server.py     # Custom SSH server implementation
├── main.py               # Main application entry point
├── requirements.txt      # Python dependencies
└── test_ssh_connection.py # SSH connection test script
```

## Installation

1. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

2. **SSH host key is already generated** in the `keys/` directory.

## Configuration

Edit `ssh_gateway/config.py` to configure:

- **Ports**: SSH server port (8024) and WebSocket server port (8003)
- **Host**: Server bind address (0.0.0.0)
- **SSH Key**: Path to SSH host key file
- **Security**: Allowed public keys for authentication

## Running the Gateway

Start the gateway server:

```bash
python main.py
```

The server will start both:
- SSH server on port 8024
- WebSocket server on port 8003

## Testing SSH Connection

### Method 1: Using the test script
```bash
python test_ssh_connection.py
```

### Method 2: Using SSH client
```bash
# Using password authentication
ssh -p 8024 device_123@localhost

# Using public key authentication (if configured)
ssh -i path/to/private/key -p 8024 device_123@localhost
```

## WebSocket Protocol

Remote devices should connect to the WebSocket endpoint:
```
ws://server_ip:8003/ws/{device_id}
```

### Message Format

**Input (SSH → Device)**:
```json
{
  "type": "input",
  "data": "command_or_data"
}
```

**Output (Device → SSH)**:
```json
{
  "type": "output", 
  "data": "result_or_response"
}
```

## Authentication

The gateway supports both:
1. **Public Key Authentication**: Configure allowed keys in `config.py`
2. **Password Authentication**: Fallback method (currently allows any password)

## Troubleshooting

### Port Already in Use
If you get port binding errors, modify the ports in `ssh_gateway/config.py`:
```python
SSH_PORT = 8024  # Change to available port
WEB_PORT = 8003  # Change to available port
```

### SSH Connection Closes Immediately
This was a common issue that has been fixed in the current implementation. The fixes include:
- Proper session initialization with welcome message
- Support for both public key and password authentication
- Better error handling and connection management
- Proper PTY and shell request handling

### No WebSocket Connection
The SSH session will show a warning if the target device is not connected via WebSocket, but the SSH connection will remain open.

## Logs

The application provides detailed logging for:
- SSH connection attempts and authentication
- WebSocket connections and disconnections
- Command forwarding between SSH and WebSocket
- Error conditions and troubleshooting information
