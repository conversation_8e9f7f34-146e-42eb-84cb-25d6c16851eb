# ssh_gateway/ssh_server.py

import asyncio
import asyncssh
import logging
from .ws_manager import manager
from . import config

class MySSHServerSession(asyncssh.SSHServerSession):
    def __init__(self, device_id: str):
        self._device_id = device_id
        self._chan = None
        self._session_active = False
        super().__init__()

    def connection_made(self, chan: asyncssh.SSHServerChannel):
        """当SSH会话通道建立时调用。"""
        self._chan = chan
        self._session_active = True
        logging.info(f"SSH session created for device '{self._device_id}'.")

        # 发送欢迎消息和初始提示符
        welcome_msg = f"Welcome to Remote SSH Gateway\nConnected to device: {self._device_id}\n"
        self._chan.write(welcome_msg)

        # 检查WebSocket连接状态
        if self._device_id not in manager.active_connections:
            self._chan.write(f"Warning: Device '{self._device_id}' is not connected via WebSocket.\n")
            self._chan.write("Waiting for device connection...\n")

        # 发送初始提示符
        self._chan.write(f"{self._device_id}$ ")

    def data_received(self, data: str, datatype: asyncssh.DataType):
        """
        接收来自Jumpserver终端的输入流。
        优化点：直接转发所有数据，以支持交互式应用(vim, top等)，而不仅仅是简单命令。
        """
        if not self._session_active:
            return

        # 使用asyncio.create_task在后台发送命令并处理结果，避免阻塞data_received
        asyncio.create_task(self.run_command(data))

    async def run_command(self, command: str):
        """将命令通过WebSocket管理器发送，并等待结果写回SSH通道。"""
        if not self._session_active:
            return

        try:
            logging.info(f"SSH -> WS ({self._device_id}): {command!r}")

            # 检查WebSocket连接是否存在
            if self._device_id not in manager.active_connections:
                self._chan.write(f"\nError: Device '{self._device_id}' is not connected via WebSocket.\n")
                self._chan.write("Please ensure the device is online and connected.\n")
                self._chan.write(f"{self._device_id}$ ")
                return

            # 调用ws_manager发送命令并等待结果
            result = await manager.send_command(self._device_id, command)
            logging.info(f"WS -> SSH ({self._device_id}): {result!r}")

            # 将结果写回给SSH客户端
            self._chan.write(result)

            # 如果结果不以换行符结尾，添加提示符
            if not result.endswith('\n'):
                self._chan.write('\n')
            self._chan.write(f"{self._device_id}$ ")

        except ConnectionError as e:
            self._chan.write(f"\nError: {e}\n")
            self._chan.write(f"{self._device_id}$ ")
        except asyncio.TimeoutError:
            self._chan.write("\nError: Command timed out.\n")
            self._chan.write(f"{self._device_id}$ ")
        except Exception as e:
            logging.error(f"Error processing command for '{self._device_id}': {e}", exc_info=True)
            self._chan.write(f"\nAn internal error occurred: {e}\n")
            self._chan.write(f"{self._device_id}$ ")

    def shell_requested(self) -> bool:
        """当客户端请求一个交互式shell时调用。"""
        logging.info(f"Shell requested for device '{self._device_id}'. Accepting.")
        # 接受shell请求，即使我们不提供一个完整的shell，
        # 这对于保持连接至关重要。
        return True

    def pty_requested(self, term: str, term_width: int, term_height: int, term_pix_width: int, term_pix_height: int, modes: bytes) -> bool:
        """当客户端请求伪终端时调用。"""
        logging.info(f"PTY requested for device '{self._device_id}'. Accepting.")
        # 接受PTY请求
        return True
        
    def connection_lost(self, exc: Exception | None) -> None:
        self._session_active = False
        if exc:
            logging.error(f"SSH session for device '{self._device_id}' closed with error: {exc}")
        else:
            logging.info(f"SSH session for device '{self._device_id}' closed normally.")


class MySSHServer(asyncssh.SSHServer):
    def session_requested(self) -> MySSHServerSession:
        """当客户端请求会话时，创建我们的自定义会话实例。"""
        device_id = self.get_extra_info('username')
        logging.info(f"Session requested for device '{device_id}'")
        return MySSHServerSession(device_id)

    def begin_auth(self, username: str) -> bool:
        """
        在密码或公钥认证前调用。
        可以在这里做一些初步的用户名检查。
        """
        logging.info(f"Beginning authentication for user '{username}'")
        # 简单地允许任何用户名，具体的路由逻辑在 session_requested 中处理
        return True

    def public_key_auth_supported(self):
        """声明我们支持公钥认证。"""
        return True

    def password_auth_supported(self):
        """声明我们支持密码认证作为备选方案。"""
        return True

    def validate_public_key(self, username: str, key: asyncssh.SSHKey) -> bool:
        """
        验证客户端提供的公钥是否在我们允许的列表中。
        """
        try:
            # 修复AsyncSSH 2.21.0版本的API变化
            # 新版本中export_public_key()不再接受format参数
            key_bytes = key.export_public_key()
            if isinstance(key_bytes, bytes):
                key_str = key_bytes.decode('utf-8').strip()
            else:
                key_str = str(key_bytes).strip()

            logging.info(f"Attempting public key auth for user '{username}' with key: {key_str[:50]}...")

            # 如果没有配置允许的公钥列表，则允许所有公钥（用于测试）
            if not config.ALLOWED_JUMPSERVER_PUBLIC_KEYS:
                logging.info(f"No public key restrictions configured. Allowing public key for '{username}'.")
                return True

            if key_str not in config.ALLOWED_JUMPSERVER_PUBLIC_KEYS:
                logging.warning(f"Public key auth failed for '{username}': Key not in allowed list.")
                return False

            logging.info(f"Public key for '{username}' validated successfully.")
            return True
        except Exception as e:
            logging.error(f"Error validating public key for '{username}': {e}")
            return False

    def validate_password(self, username: str, password: str) -> bool:
        """
        验证密码认证。这里我们允许任何密码作为备选方案。
        """
        logging.info(f"Password authentication attempted for user '{username}'")
        # 在生产环境中，这里应该有真正的密码验证逻辑
        # 现在我们允许任何密码，主要用于保持连接
        return True
