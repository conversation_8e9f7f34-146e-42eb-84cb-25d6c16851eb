#!/usr/bin/env python3
"""
Test script to verify SSH connection to the gateway works properly.
"""

import asyncio
import asyncssh
import logging

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_ssh_connection():
    """Test SSH connection to the gateway."""
    try:
        # Connect to the SSH gateway
        logger.info("Attempting to connect to SSH gateway...")
        
        # Use password authentication for testing
        conn = await asyncssh.connect(
            host='localhost',
            port=8024,
            username='test_device_123',
            password='test_password',
            known_hosts=None  # Disable host key checking for testing
        )
        
        logger.info("SSH connection established successfully!")
        
        # Test sending a command
        logger.info("Testing command execution...")
        result = await conn.run('echo "Hello from SSH gateway!"')
        
        logger.info(f"Command result: {result.stdout}")
        logger.info(f"Command stderr: {result.stderr}")
        logger.info(f"Command exit status: {result.exit_status}")
        
        # Keep connection alive for a bit
        await asyncio.sleep(2)
        
        # Close connection
        conn.close()
        await conn.wait_closed()
        
        logger.info("SSH connection closed successfully!")
        
    except Exception as e:
        logger.error(f"SSH connection test failed: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = asyncio.run(test_ssh_connection())
    if success:
        print("✅ SSH connection test passed!")
    else:
        print("❌ SSH connection test failed!")
